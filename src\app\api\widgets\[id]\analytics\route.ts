import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/db';
import { widget, analytics, feedback } from '@/db/schema';
import { eq, and, desc, count, gte } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: widgetId } = await params;
    const userId = session.user.id;

    // Verify widget belongs to user
    const userWidget = await db
      .select()
      .from(widget)
      .where(and(
        eq(widget.id, widgetId),
        eq(widget.userId, userId)
      ))
      .limit(1);

    if (userWidget.length === 0) {
      return NextResponse.json(
        { error: 'Widget not found' },
        { status: 404 }
      );
    }

    // Get total message count for this widget
    const [totalMessages] = await db
      .select({ count: count() })
      .from(analytics)
      .where(and(
        eq(analytics.widgetId, widgetId),
        eq(analytics.eventType, 'message_sent')
      ));

    // Get total feedback count for this widget
    const [totalFeedbacks] = await db
      .select({ count: count() })
      .from(feedback)
      .where(and(
        eq(feedback.widgetId, widgetId),
        eq(feedback.type, 'feedback')
      ));

    // Get total bug reports for this widget
    const [totalBugReports] = await db
      .select({ count: count() })
      .from(feedback)
      .where(and(
        eq(feedback.widgetId, widgetId),
        eq(feedback.type, 'bug')
      ));

    // Get total feature requests for this widget
    const [totalFeatureRequests] = await db
      .select({ count: count() })
      .from(feedback)
      .where(and(
        eq(feedback.widgetId, widgetId),
        eq(feedback.type, 'feature')
      ));

    // Get recent feedbacks for this widget (last 10)
    const recentFeedbacks = await db
      .select({
        id: feedback.id,
        content: feedback.content,
        type: feedback.type,
        createdAt: feedback.createdAt,
      })
      .from(feedback)
      .where(and(
        eq(feedback.widgetId, widgetId),
        eq(feedback.type, 'feedback')
      ))
      .orderBy(desc(feedback.createdAt))
      .limit(10);

    // Get recent bug reports for this widget (last 10)
    const recentBugReports = await db
      .select({
        id: feedback.id,
        content: feedback.content,
        createdAt: feedback.createdAt,
      })
      .from(feedback)
      .where(and(
        eq(feedback.widgetId, widgetId),
        eq(feedback.type, 'bug')
      ))
      .orderBy(desc(feedback.createdAt))
      .limit(10);

    // Get messages over time (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const messagesOverTime = await db
      .select({
        date: analytics.createdAt,
        count: count()
      })
      .from(analytics)
      .where(and(
        eq(analytics.widgetId, widgetId),
        eq(analytics.eventType, 'message_sent'),
        gte(analytics.createdAt, thirtyDaysAgo)
      ))
      .groupBy(analytics.createdAt)
      .orderBy(analytics.createdAt);

    return NextResponse.json({
      totalMessages: totalMessages.count || 0,
      totalFeedbacks: totalFeedbacks.count || 0,
      totalBugReports: totalBugReports.count || 0,
      totalFeatureRequests: totalFeatureRequests.count || 0,
      recentFeedbacks: recentFeedbacks,
      recentBugReports: recentBugReports,
      messagesOverTime: messagesOverTime,
    });

  } catch (error) {
    console.error('Error fetching widget analytics:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
