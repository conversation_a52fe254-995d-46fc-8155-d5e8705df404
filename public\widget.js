// Modern Support Widget
class ModernSupportWidget {
  constructor(config) {
    console.log('ModernSupportWidget: Constructor called with config:', config);

    this.config = {
      widgetId: config.widgetId,
      position: config.position || 'bottom-right',
      primaryColor: config.primaryColor || '#6366F1',
      productType: config.productType || 'saas',
      productName: config.productName || 'Support',
      features: config.features || [],
      description: config.description || '',
      faqs: config.faqs || [],
      widgetTitle: config.widgetTitle || 'Need Help?',
      welcomeMessage: config.welcomeMessage || 'How can we help you today?',
      feedbackQuestion: config.feedbackQuestion || '',
      enableBugReports: config.enableBugReports !== false,
      isActive: config.isActive !== false,
      ...config
    };

    console.log('ModernSupportWidget: Final config:', this.config);

    this.isOpen = false;
    this.currentView = 'main'; // main, chat, feedback, bug
    this.messages = [];

    if (this.config.isActive) {
      console.log('ModernSupportWidget: Widget is active, initializing...');
      this.init().catch(error => {
        console.error('Widget initialization failed:', error);
        // Fallback to basic initialization
        this.createStyles();
        this.createWidget();
        this.attachEventListeners();
      });
    } else {
      console.log('ModernSupportWidget: Widget is not active');
    }
  }

  async init() {
    console.log('ModernSupportWidget: init() called');

    // Load widget config from server if widgetId is provided
    if (this.config.widgetId) {
      console.log('ModernSupportWidget: Loading config from server for widget ID:', this.config.widgetId);
      await this.loadWidgetConfig();
    }

    // Create styles after config is loaded so they use the correct values
    console.log('ModernSupportWidget: Creating styles...');
    this.createStyles();

    console.log('ModernSupportWidget: Creating widget...');
    this.createWidget();

    console.log('ModernSupportWidget: Attaching event listeners...');
    this.attachEventListeners();

    // Update widget appearance after everything is created
    console.log('ModernSupportWidget: Updating widget appearance...');
    this.updateWidgetAppearance();

    console.log('ModernSupportWidget: Initialization complete!');
  }

  createStyles() {
    // Remove existing styles if any
    const existingStyle = document.getElementById('modern-support-widget-styles');
    if (existingStyle) {
      existingStyle.remove();
    }

    const style = document.createElement('style');
    style.id = 'modern-support-widget-styles';
    style.textContent = `
      .modern-support-widget {
        position: fixed;
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      }
      
      .modern-support-widget.bottom-right { bottom: 20px; right: 20px; }
      .modern-support-widget.bottom-left { bottom: 20px; left: 20px; }
      .modern-support-widget.top-right { top: 20px; right: 20px; }
      .modern-support-widget.top-left { top: 20px; left: 20px; }
      
      .widget-trigger-btn {
        background: ${this.config.primaryColor};
        color: white;
        border: none;
        border-radius: 25px;
        padding: 12px 16px;
        cursor: pointer;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid rgba(255,255,255,0.1);
      }
      
      .widget-trigger-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 32px rgba(0,0,0,0.2);
      }
      
      .widget-popup {
        position: absolute;
        width: 320px;
        background: white;
        border-radius: 16px;
        box-shadow: 0 20px 60px rgba(0,0,0,0.15);
        overflow: hidden;
        transform: translateY(20px) scale(0.95);
        opacity: 0;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        pointer-events: none;
        max-height: 500px;
        display: flex;
        flex-direction: column;
      }
      
      .widget-popup.open {
        transform: translateY(0) scale(1);
        opacity: 1;
        pointer-events: all;
      }
      
      .widget-popup.bottom-right, .widget-popup.bottom-left { bottom: 70px; }
      .widget-popup.top-right, .widget-popup.top-left { top: 70px; }
      .widget-popup.bottom-right, .widget-popup.top-right { right: 0; }
      .widget-popup.bottom-left, .widget-popup.top-left { left: 0; }
      
      .widget-header {
        background: ${this.config.primaryColor};
        color: white;
        padding: 20px 24px;
        display: flex;
        align-items: center;
        gap: 12px;
        min-height: 80px;
      }
      
      .widget-header h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        line-height: 1.2;
      }
      
      .widget-header p {
        margin: 4px 0 0 0;
        font-size: 14px;
        opacity: 0.9;
        line-height: 1.3;
      }
      
      .back-btn {
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        padding: 8px;
        border-radius: 8px;
        transition: background-color 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 4px;
      }
      
      .back-btn:hover {
        background: rgba(255,255,255,0.1);
      }
      
      .widget-options {
        padding: 24px;
        display: flex;
        flex-direction: column;
        gap: 12px;
      }
      
      .widget-option {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 16px;
        cursor: pointer;
        text-align: left;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        gap: 16px;
      }
      
      .widget-option:hover {
        background: #f1f5f9;
        border-color: #cbd5e1;
        transform: translateY(-1px);
      }
      
      .option-icon {
        padding: 10px;
        border-radius: 10px;
        background: ${this.config.primaryColor};
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 40px;
        height: 40px;
      }
      
      .option-content {
        flex: 1;
      }
      
      .option-title {
        font-weight: 600;
        font-size: 14px;
        color: #1e293b;
        margin: 0 0 4px 0;
      }
      
      .option-desc {
        font-size: 12px;
        color: #64748b;
        margin: 0;
        line-height: 1.3;
      }
      
      .widget-view {
        display: none;
      }
      
      .widget-view.active {
        display: flex;
        flex-direction: column;
        flex: 1;
      }
      
      .widget-chat {
        display: flex;
        flex-direction: column;
        height: 400px;
      }
      
      .chat-messages {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        gap: 12px;
        background: #fafbfc;
      }
      
      .chat-message {
        max-width: 85%;
        padding: 12px 16px;
        border-radius: 18px;
        font-size: 14px;
        line-height: 1.4;
        word-wrap: break-word;
      }
      
      .chat-message.user {
        background: ${this.config.primaryColor};
        color: white;
        align-self: flex-end;
        border-bottom-right-radius: 4px;
      }
      
      .chat-message.bot {
        background: white;
        color: #374151;
        align-self: flex-start;
        border: 1px solid #e5e7eb;
        border-bottom-left-radius: 4px;
      }
      
      .chat-input-container {
        padding: 20px;
        border-top: 1px solid #e5e7eb;
        background: white;
        display: flex;
        gap: 12px;
        align-items: end;
      }
      
      .chat-input {
        flex: 1;
        border: 1px solid #d1d5db;
        border-radius: 20px;
        padding: 12px 16px;
        font-size: 14px;
        outline: none;
        resize: none;
        min-height: 20px;
        max-height: 100px;
        font-family: inherit;
        transition: border-color 0.2s;
      }
      
      .chat-input:focus {
        border-color: ${this.config.primaryColor};
        box-shadow: 0 0 0 3px ${this.config.primaryColor}20;
      }
      
      .chat-send-btn {
        background: ${this.config.primaryColor};
        color: white;
        border: none;
        border-radius: 50%;
        width: 44px;
        height: 44px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s;
        flex-shrink: 0;
      }
      
      .chat-send-btn:hover {
        background: ${this.config.primaryColor}dd;
        transform: scale(1.05);
      }
      
      .chat-send-btn:disabled {
        background: #9ca3af;
        cursor: not-allowed;
        transform: none;
      }
      
      .widget-form {
        padding: 24px;
        background: white;
      }
      
      .form-label {
        display: block;
        font-size: 14px;
        font-weight: 600;
        color: #374151;
        margin-bottom: 8px;
      }
      
      .form-textarea {
        width: 100%;
        border: 1px solid #d1d5db;
        border-radius: 12px;
        padding: 12px 16px;
        font-size: 14px;
        resize: vertical;
        min-height: 100px;
        font-family: inherit;
        box-sizing: border-box;
        transition: border-color 0.2s;
      }
      
      .form-textarea:focus {
        outline: none;
        border-color: ${this.config.primaryColor};
        box-shadow: 0 0 0 3px ${this.config.primaryColor}20;
      }
      
      .form-submit-btn {
        background: ${this.config.primaryColor};
        color: white;
        border: none;
        border-radius: 12px;
        padding: 14px 24px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        margin-top: 16px;
        width: 100%;
        transition: all 0.2s;
      }
      
      .form-submit-btn:hover {
        background: ${this.config.primaryColor}dd;
        transform: translateY(-1px);
      }
      
      .form-submit-btn:disabled {
        background: #9ca3af;
        cursor: not-allowed;
        transform: none;
      }
      
      .success-message {
        text-align: center;
        padding: 40px 24px;
      }
      
      .success-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }
      
      .success-title {
        font-size: 18px;
        font-weight: 600;
        color: #059669;
        margin: 0 0 8px 0;
      }
      
      .success-desc {
        font-size: 14px;
        color: #6b7280;
        margin: 0;
      }
      
      .typing-indicator {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 12px 16px;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 18px;
        border-bottom-left-radius: 4px;
        max-width: 85%;
        align-self: flex-start;
      }
      
      .typing-dot {
        width: 8px;
        height: 8px;
        background: #9ca3af;
        border-radius: 50%;
        animation: typing 1.4s infinite ease-in-out;
      }
      
      .typing-dot:nth-child(1) { animation-delay: -0.32s; }
      .typing-dot:nth-child(2) { animation-delay: -0.16s; }
      
      @keyframes typing {
        0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
        40% { transform: scale(1); opacity: 1; }
      }
      
      @media (max-width: 480px) {
        .widget-popup {
          width: calc(100vw - 40px);
          max-width: 320px;
        }
      }
    `;
    document.head.appendChild(style);
  }

  createWidget() {
    console.log('ModernSupportWidget: createWidget() called');

    // Create widget container
    this.container = document.createElement('div');
    this.container.className = `modern-support-widget ${this.config.position}`;
    console.log('ModernSupportWidget: Created container with class:', this.container.className);

    // Create trigger button
    this.button = document.createElement('button');
    this.button.className = 'widget-trigger-btn';
    this.button.innerHTML = `
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
      </svg>
      <span>${this.config.widgetTitle}</span>
    `;
    console.log('ModernSupportWidget: Created button with title:', this.config.widgetTitle);

    // Create popup
    this.popup = document.createElement('div');
    this.popup.className = `widget-popup ${this.config.position}`;
    console.log('ModernSupportWidget: Created popup with class:', this.popup.className);

    console.log('ModernSupportWidget: Creating views...');
    this.createMainView();
    this.createChatView();
    this.createFormViews();

    this.container.appendChild(this.button);
    this.container.appendChild(this.popup);

    console.log('ModernSupportWidget: Appending widget to body...');
    document.body.appendChild(this.container);

    console.log('ModernSupportWidget: Widget created and added to DOM!');
  }

  createMainView() {
    this.mainView = document.createElement('div');
    this.mainView.className = 'widget-view widget-main-view active';

    // Header
    const header = document.createElement('div');
    header.className = 'widget-header';
    header.innerHTML = `
      <div>
        <h3>${this.config.productName}</h3>
        <p>${this.config.welcomeMessage}</p>
      </div>
    `;

    // Options
    const options = document.createElement('div');
    options.className = 'widget-options';

    const availableOptions = this.getAvailableOptions();
    availableOptions.forEach(option => {
      const button = document.createElement('button');
      button.className = 'widget-option';
      button.innerHTML = `
        <div class="option-icon">
          ${option.icon}
        </div>
        <div class="option-content">
          <div class="option-title">${option.title}</div>
          <div class="option-desc">${option.desc}</div>
        </div>
      `;
      button.addEventListener('click', () => this.showView(option.action));
      options.appendChild(button);
    });

    this.mainView.appendChild(header);
    this.mainView.appendChild(options);
    this.popup.appendChild(this.mainView);
  }

  createChatView() {
    this.chatView = document.createElement('div');
    this.chatView.className = 'widget-view widget-chat-view';

    // Header with back button
    const header = document.createElement('div');
    header.className = 'widget-header';
    header.innerHTML = `
      <button class="back-btn">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="m15 18-6-6 6-6"/>
        </svg>
      </button>
      <div>
        <h3>Chat with Agent</h3>
        <p>We're here to help you!</p>
      </div>
    `;
    header.querySelector('.back-btn').addEventListener('click', () => this.showView('main'));

    // Chat container
    const chat = document.createElement('div');
    chat.className = 'widget-chat';

    // Messages container
    this.messagesContainer = document.createElement('div');
    this.messagesContainer.className = 'chat-messages';

    // Input container
    const inputContainer = document.createElement('div');
    inputContainer.className = 'chat-input-container';

    this.chatInput = document.createElement('textarea');
    this.chatInput.className = 'chat-input';
    this.chatInput.placeholder = 'Type your message...';
    this.chatInput.rows = 1;
    this.chatInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.sendMessage();
      }
    });
    this.chatInput.addEventListener('input', this.autoResizeTextarea);

    this.sendButton = document.createElement('button');
    this.sendButton.className = 'chat-send-btn';
    this.sendButton.innerHTML = `
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <line x1="22" y1="2" x2="11" y2="13"></line>
        <polygon points="22,2 15,22 11,13 2,9 22,2"></polygon>
      </svg>
    `;
    this.sendButton.addEventListener('click', () => this.sendMessage());

    inputContainer.appendChild(this.chatInput);
    inputContainer.appendChild(this.sendButton);

    chat.appendChild(this.messagesContainer);
    chat.appendChild(inputContainer);

    this.chatView.appendChild(header);
    this.chatView.appendChild(chat);
    this.popup.appendChild(this.chatView);

    // Add initial bot message
    this.addMessage('bot', this.config.welcomeMessage);
  }

  createFormViews() {
    const formTypes = [];
    
    if (this.config.feedbackQuestion) {
      formTypes.push({
        type: 'feedback',
        title: 'Give Feedback',
        placeholder: this.config.feedbackQuestion
      });
    }
    
    if (this.config.enableBugReports) {
      formTypes.push({
        type: 'bug',
        title: 'Report Bug',
        placeholder: 'Please describe the issue you encountered...'
      });
    }

    formTypes.forEach(formType => {
      const view = document.createElement('div');
      view.className = `widget-view widget-${formType.type}-view`;

      // Header with back button
      const header = document.createElement('div');
      header.className = 'widget-header';
      header.innerHTML = `
        <button class="back-btn">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="m15 18-6-6 6-6"/>
          </svg>
        </button>
        <div>
          <h3>${formType.title}</h3>
          <p>Your input helps us improve</p>
        </div>
      `;
      header.querySelector('.back-btn').addEventListener('click', () => this.showView('main'));

      // Form
      const form = document.createElement('div');
      form.className = 'widget-form';

      const label = document.createElement('label');
      label.className = 'form-label';
      label.textContent = formType.placeholder;

      const textarea = document.createElement('textarea');
      textarea.className = 'form-textarea';
      textarea.placeholder = `Please share your ${formType.type === 'feedback' ? 'feedback' : 'bug report'}...`;
      textarea.required = true;

      const submitButton = document.createElement('button');
      submitButton.className = 'form-submit-btn';
      submitButton.textContent = `Submit ${formType.title}`;
      submitButton.addEventListener('click', () => this.submitForm(formType.type, textarea.value, form));

      form.appendChild(label);
      form.appendChild(textarea);
      form.appendChild(submitButton);

      view.appendChild(header);
      view.appendChild(form);
      this.popup.appendChild(view);
    });
  }

  getAvailableOptions() {
    const options = [];
    
    // Always show chat option
    options.push({
      icon: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
               <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
             </svg>`,
      title: 'Talk to Agent',
      desc: 'Start a conversation with our AI assistant',
      action: 'chat'
    });

    // Show feedback option if question is provided
    if (this.config.feedbackQuestion) {
      options.push({
        icon: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                 <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"/>
               </svg>`,
        title: 'Give Feedback',
        desc: 'Share your thoughts and suggestions',
        action: 'feedback'
      });
    }

    // Show bug report option if enabled
    if (this.config.enableBugReports) {
      options.push({
        icon: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                 <rect x="8" y="6" width="8" height="14" rx="4"/>
                 <path d="m9 9-5 3 5 3"/>
                 <path d="m15 9 5 3-5 3"/>
               </svg>`,
        title: 'Report Bug',
        desc: 'Report any issues you encountered',
        action: 'bug'
      });
    }

    return options;
  }

  attachEventListeners() {
    this.button.addEventListener('click', () => this.toggle());

    // Close popup when clicking outside
    document.addEventListener('click', (e) => {
      if (!this.container.contains(e.target) && this.isOpen) {
        this.close();
      }
    });

    // Close on escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isOpen) {
        this.close();
      }
    });
  }

  toggle() {
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }

  open() {
    this.isOpen = true;
    this.popup.style.display = 'flex';
    setTimeout(() => {
      this.popup.classList.add('open');
    }, 10);
  }

  close() {
    this.isOpen = false;
    this.popup.classList.remove('open');
    setTimeout(() => {
      this.popup.style.display = 'none';
      this.showView('main'); // Reset to main view
    }, 300);
  }

  showView(viewName) {
    // Hide all views
    this.popup.querySelectorAll('.widget-view').forEach(view => {
      view.classList.remove('active');
    });

    // Show selected view
    const targetView = this.popup.querySelector(`.widget-${viewName}-view`);
    if (targetView) {
      targetView.classList.add('active');
      this.currentView = viewName;
      
      // Focus input if chat view
      if (viewName === 'chat' && this.chatInput) {
        setTimeout(() => this.chatInput.focus(), 100);
      }
    }
  }

  addMessage(type, content) {
    const message = document.createElement('div');
    message.className = `chat-message ${type}`;
    message.textContent = content;
    this.messagesContainer.appendChild(message);
    this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    this.messages.push({ type, content, timestamp: Date.now() });
  }

  showTypingIndicator() {
    const indicator = document.createElement('div');
    indicator.className = 'typing-indicator';
    indicator.innerHTML = `
      <div class="typing-dot"></div>
      <div class="typing-dot"></div>
      <div class="typing-dot"></div>
    `;
    this.messagesContainer.appendChild(indicator);
    this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    return indicator;
  }

  async sendMessage() {
    const message = this.chatInput.value.trim();
    if (!message) return;

    this.addMessage('user', message);
    this.chatInput.value = '';
    this.autoResizeTextarea.call(this.chatInput);
    this.sendButton.disabled = true;

    // Show typing indicator
    const typingIndicator = this.showTypingIndicator();

    try {
      // Call the actual API
      const response = await this.sendChatMessage(message);

      // Remove typing indicator
      if (this.messagesContainer.contains(typingIndicator)) {
        this.messagesContainer.removeChild(typingIndicator);
      }

      // Add bot response
      this.addMessage('bot', response);

    } catch (error) {
      console.error('Chat error:', error);
      if (this.messagesContainer.contains(typingIndicator)) {
        this.messagesContainer.removeChild(typingIndicator);
      }
      this.addMessage('bot', 'Sorry, I encountered an error. Please try again.');
    } finally {
      this.sendButton.disabled = false;
    }
  }

  async submitForm(type, content, formContainer) {
    if (!content.trim()) return;

    const submitButton = formContainer.querySelector('.form-submit-btn');

    submitButton.disabled = true;
    submitButton.textContent = 'Submitting...';

    try {
      // Call the actual API
      const success = await this.submitFeedbackOrBug(type, content);

      if (success) {
        // Show success message
        formContainer.innerHTML = `
          <div class="success-message">
            <div class="success-icon">✅</div>
            <h3 class="success-title">Thank you!</h3>
            <p class="success-desc">Your ${type} has been submitted successfully. We'll review it and get back to you soon.</p>
          </div>
        `;

        // Auto close after 3 seconds
        setTimeout(() => {
          this.showView('main');
          // Reset form after a delay
          setTimeout(() => {
            this.resetForm(type);
          }, 300);
        }, 3000);
      } else {
        throw new Error('Submission failed');
      }

    } catch (error) {
      console.error('Submit error:', error);
      submitButton.disabled = false;
      submitButton.textContent = `Submit ${type === 'feedback' ? 'Feedback' : 'Bug Report'}`;

      // Show error message
      const errorDiv = document.createElement('div');
      errorDiv.style.cssText = 'color: #ef4444; font-size: 14px; margin-top: 8px; text-align: center; padding: 8px; background: #fef2f2; border-radius: 8px; border: 1px solid #fecaca;';
      errorDiv.textContent = 'Failed to submit. Please try again.';
      formContainer.appendChild(errorDiv);

      setTimeout(() => {
        if (formContainer.contains(errorDiv)) {
          formContainer.removeChild(errorDiv);
        }
      }, 5000);
    }
  }

  resetForm(type) {
    const view = this.popup.querySelector(`.widget-${type}-view`);
    const form = view.querySelector('.widget-form');
    
    // Recreate the form
    const formType = type === 'feedback' ? {
      type: 'feedback',
      title: 'Give Feedback',
      placeholder: this.config.feedbackQuestion
    } : {
      type: 'bug',
      title: 'Report Bug',
      placeholder: 'Please describe the issue you encountered...'
    };

    const label = document.createElement('label');
    label.className = 'form-label';
    label.textContent = formType.placeholder;

    const textarea = document.createElement('textarea');
    textarea.className = 'form-textarea';
    textarea.placeholder = `Please share your ${type === 'feedback' ? 'feedback' : 'bug report'}...`;
    textarea.required = true;

    const submitButton = document.createElement('button');
    submitButton.className = 'form-submit-btn';
    submitButton.textContent = `Submit ${formType.title}`;
    submitButton.addEventListener('click', () => this.submitForm(type, textarea.value, form));

    form.innerHTML = '';
    form.appendChild(label);
    form.appendChild(textarea);
    form.appendChild(submitButton);
  }

  autoResizeTextarea() {
    this.style.height = 'auto';
    this.style.height = Math.min(this.scrollHeight, 100) + 'px';
  }

  async loadWidgetConfig() {
    try {
      // Use relative URL to work with any domain
      const apiUrl = `http://localhost:3000/api/widgets/${this.config.widgetId}`;
      console.log('ModernSupportWidget: Fetching config from:', apiUrl);
      const response = await fetch(apiUrl);

      if (response.ok) {
        const data = await response.json();
        console.log('ModernSupportWidget: Received config from server:', data);
        // Update config with server data
        Object.assign(this.config, data.widget);
        console.log('ModernSupportWidget: Updated config:', this.config);
      } else {
        console.warn('ModernSupportWidget: Failed to load config from server, using default config');
      }
    } catch (error) {
      console.error('ModernSupportWidget: Failed to load widget config:', error);
      console.log('ModernSupportWidget: Continuing with default config');
    }
  }

  updateWidgetAppearance() {
    // Update button text
    if (this.button) {
      const span = this.button.querySelector('span');
      if (span) {
        span.textContent = this.config.widgetTitle;
      }
    }

    // Update header content
    const headers = this.popup.querySelectorAll('.widget-header h3');
    headers.forEach(header => {
      if (header.textContent === 'Chat with Agent') return;
      if (header.textContent === 'Give Feedback') return;
      if (header.textContent === 'Report Bug') return;
      header.textContent = this.config.productName;
    });

    const welcomeMessages = this.popup.querySelectorAll('.widget-header p');
    welcomeMessages.forEach(p => {
      if (p.textContent.includes('Your input helps us improve')) return;
      if (p.textContent.includes('We\'re here to help you!')) return;
      p.textContent = this.config.welcomeMessage;
    });
  }

  async sendChatMessage(message) {
    try {
      // Use relative URL to work with any domain
      const apiUrl = `http://localhost:3000/api/widgets/${this.config.widgetId}/chat`;
      console.log('ModernSupportWidget: Sending chat message to:', apiUrl);

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message }),
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      const data = await response.json();
      return data.response || 'Thank you for your message. We\'ll get back to you soon!';
    } catch (error) {
      console.error('Chat error:', error);
      throw error; // Re-throw to be handled by the calling function
    }
  }

  async submitFeedbackOrBug(type, content) {
    try {
      // Use relative URL to work with any domain
      const apiUrl = 'http://localhost:3000/api/feedback';
      console.log('ModernSupportWidget: Submitting feedback to:', apiUrl);
      console.log('ModernSupportWidget: Payload:', {
        widgetId: this.config.widgetId,
        type: type === 'feedback' ? 'feedback' : 'bug',
        content,
      });

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          widgetId: this.config.widgetId,
          type: type === 'feedback' ? 'feedback' : 'bug',
          content,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('ModernSupportWidget: API Error Response:', errorText);
        throw new Error('Failed to submit');
      }

      return true;
    } catch (error) {
      console.error('Submit error:', error);
      throw error; // Re-throw to be handled by the calling function
    }
  }
}

// Auto-initialize widget
(function() {
  if (typeof window === 'undefined') return;

  // Make class available globally
  window.ModernSupportWidget = ModernSupportWidget;

  // Auto-initialize from script tag data attributes
  function autoInitialize() {
    console.log('ModernSupportWidget: Initializing...');

    // Look for script tag with data-widget-id
    const scripts = document.querySelectorAll('script[data-widget-id]');
    console.log('ModernSupportWidget: Found', scripts.length, 'script tags with data-widget-id');

    scripts.forEach(script => {
      const widgetId = script.getAttribute('data-widget-id');
      if (widgetId) {
        const config = {
          widgetId: widgetId,
          position: script.getAttribute('data-position') || 'bottom-right',
          primaryColor: script.getAttribute('data-primary-color') || '#6366F1',
          productType: script.getAttribute('data-product-type') || 'saas',
          productName: script.getAttribute('data-product-name') || 'Support',
          widgetTitle: script.getAttribute('data-widget-title') || 'Need Help?',
          welcomeMessage: script.getAttribute('data-welcome-message') || 'How can we help you today?',
          feedbackQuestion: script.getAttribute('data-feedback-question') || '',
          enableBugReports: script.getAttribute('data-enable-bug-reports') !== 'false',
          isActive: script.getAttribute('data-is-active') !== 'false'
        };

        console.log('ModernSupportWidget: Creating widget with config:', config);
        new ModernSupportWidget(config);
      }
    });

    // Also check for global config
    if (window.ModernSupportWidgetConfig) {
      console.log('ModernSupportWidget: Creating widget from global config');
      new ModernSupportWidget(window.ModernSupportWidgetConfig);
    }

    if (scripts.length === 0 && !window.ModernSupportWidgetConfig) {
      console.log('ModernSupportWidget: No configuration found');
    }
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', autoInitialize);
  } else {
    autoInitialize();
  }
})();

// Export for manual initialization
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ModernSupportWidget;
}