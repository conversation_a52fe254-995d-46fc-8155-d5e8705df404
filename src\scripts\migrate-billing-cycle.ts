/**
 * Migration script to add billingCycleDay to existing subscriptions
 * This should be run once to update existing subscriptions with proper billing cycle days
 */

import { db } from '@/db';
import { subscription, user } from '@/db/schema';
import { eq } from 'drizzle-orm';

async function migrateBillingCycles() {
  console.log('Starting billing cycle migration...');
  
  try {
    // Get all subscriptions that don't have a billingCycleDay set
    const subscriptions = await db
      .select({
        id: subscription.id,
        userId: subscription.userId,
        createdAt: subscription.createdAt,
        billingCycleDay: subscription.billingCycleDay,
      })
      .from(subscription)
      .where(eq(subscription.billingCycleDay, 1)); // Default value, likely needs updating

    console.log(`Found ${subscriptions.length} subscriptions to migrate`);

    for (const sub of subscriptions) {
      // Get the user's signup date
      const userRecord = await db
        .select({ createdAt: user.createdAt })
        .from(user)
        .where(eq(user.id, sub.userId))
        .limit(1);

      if (userRecord.length > 0) {
        const signupDate = userRecord[0].createdAt || sub.createdAt;
        const billingCycleDay = signupDate.getDate();

        // Update the subscription with the correct billing cycle day
        await db
          .update(subscription)
          .set({ billingCycleDay })
          .where(eq(subscription.id, sub.id));

        console.log(`Updated subscription ${sub.id} with billing cycle day: ${billingCycleDay}`);
      }
    }

    console.log('Billing cycle migration completed successfully');
  } catch (error) {
    console.error('Error during billing cycle migration:', error);
    throw error;
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  migrateBillingCycles()
    .then(() => {
      console.log('Migration completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

export { migrateBillingCycles };
